﻿========================================================================
    APLICACIÓN DE CONSOLA: EncDec 
                           Información general del proyecto
========================================================================

AppWizard ha creado esta aplicación EncDec.

Este archivo incluye un resumen acerca del contenido de los archivos que 
constituyen su aplicación EncDec.


EncDec.vcxproj
    Éste es el archivo de proyecto principal para los proyectos de VC++ 
    generados mediante un Asistente para aplicaciones.
    Contiene información acerca de la versión de Visual C++ con la que 
    se generó el archivo, así como información acerca de las plataformas, 
    configuraciones y características del proyecto seleccionadas en el 
    asistente para aplicaciones.

EncDec.vcxproj.filters
    Éste es el archivo de filtros para los proyectos de VC++ generados 
    mediante un asistente para aplicaciones. 
    Contiene información acerca de la asociación entre los archivos de 
    un proyecto y los filtros. Esta asociación se usa en el IDE para mostrar 
    la agrupación de archivos con extensiones similares bajo un nodo 
    específico (por ejemplo, los archivos ".cpp" se asocian con el filtro
    "Archivos de código fuente").

EncDec.cpp
    Ésta es la aplicación principal del archivo de código fuente.

/////////////////////////////////////////////////////////////////////////////
Otros archivos estándar:

StdAfx.h, StdAfx.cpp
    Estos archivos se utilizan para crear un archivo de encabezado precompilado 
    (PCH) denominado EncDec.pch y un archivo de tipos 
    precompilado denominado StdAfx.obj.

/////////////////////////////////////////////////////////////////////////////
Otras notas:

El asistente para aplicaciones utiliza comentarios "TODO:" para indicar las 
partes del código fuente que tendrá que agregar o personalizar.

/////////////////////////////////////////////////////////////////////////////
