[MainInfo]
ClientName = Main.exe
LauncherType =0
LauncherName =LauncherWindowName
MaxOfInstance = 2
PluginAntiHack = 
PluginReShader = 
PathExport = F:\MuOnline\MuFile\Client

[HelperInfo]
;==================================================
; MuHelper settings level active
;==================================================
helperAlertEnabled = 0
helperAlertMinLevel = 80

[ReconnectInfo]
;==================================================
; System Reconnection settings time waiting
; reconnectDelayMs = 0 (Disable)
;==================================================
reconnectDelayMs = 300000

[CharacterInfo]
;==================================================
; Maximum speed in character agility points
;==================================================
DWMaxAttackSpeed = 67000
DKMaxAttackSpeed = 65000
FEMaxAttackSpeed = 67000
MGMaxAttackSpeed = 67000
DLMaxAttackSpeed = 67000
SUMaxAttackSpeed = 67000
RFMaxAttackSpeed = 67000

[Custom]
;==================================================
; Render logo in screen shot
; Req: Data\Interface\HUD\MU-logo.OZT
; 0: Disable
; 1: Left Top position
; 2: Right Top position
; 3: Left Buttom position
; 4: Right Buttom position
; 5: Center position
;==================================================
ScreenMuLogo = 4

;==================================================
; Remove Character in creation player
; 0: Normal (No remove)
; 1: Remove Summoner/DarkLord/RageFighter
; 2: Remove Summoner/RageFighter
; 3: Remove RageFighter
;==================================================
blockedClasses = 0

;==================================================
; Set stable fps quality
; maximun drop fps onlyne
;==================================================
fpsRenderMode = 60
FpsWindowsOption = 1

;==================================================
; Render Logo guild character in conversation
; 0: Disable
; 1: Enable
;==================================================
showGuildLogos = 1

;==================================================
; Enter the maximum level number on the server
; maximum level: 10 ~ 15
; - mix plus item level1 ~ item level 10
; - mix plus item level2 ~ item level 11
; - mix plus item level3 ~ item level 12
; - mix plus item level4 ~ item level 13
; - mix plus item level5 ~ item level 14
; - mix plus item level6 ~ item level 15
;==================================================
MaxItemLevelOn = 15

;==================================================
; turn off or turn on the mix system of the wings
; level 3 of the 3rd quest for players
; - Mix Feather Of Condor
; - Mix Level Wings 3
;==================================================
ShutdownLevelWing3 = 1

;==================================================
; Enable coin trading
; - 0: Disable
; - 1: Enable
;==================================================
advancedTradeSystem = 1

;==================================================
; Enable remember account
; - 0: Disable
; - 1: Enable
;==================================================
EnableRememberAccount = 1

;==================================================
; Enable jewel of bank
; - 0: Disable
; - 1: Enable
;==================================================
EnableJewelOfInventory = 0

;==================================================
; Enable Windows of Reset Information
; - 0: Disable
; - 1: Enable
;==================================================
EnableWindowsResetInfo = 1

;==================================================
; Enable Cs Skills on all maps
; You must enable it in the server's Common.data as well
; 1: Active All maps
; 0: Only active in Castle Siegue
;==================================================
castleSkillsEnabled = 1

;==================================================
; Settings modality Rank User (RankUserSwitch)
; 0: Complete
; 1: No Show Stats
; 2: No Show Logo
; 3: No Show Stats/Logo
; 4: Show Logo Over Head
; 5: Remove (Disable)
;==================================================
rankUserFeatureEnabled = 0
showRankAboveHeads = 1
showRankOnlyInSafeZone = 1
showRankResetCount = 1
showRankMasterResetCount = 1
rankDisplayRequiresAltKey = 0

;==================================================
; Render Hp Party Type
; 0: Normal
; 1: Icon
; 2: Disable
;==================================================
partyHpBarStyle = 1

;==================================================
; Enable Menu System
; Settings (CustomMenuSwitch)
; 0: Disable
; 1: Enable
; Settings (CustomMenuType)
; 1: Render Type texture logo
; 2: Render Type Button in MuHelper
;==================================================
CustomMenuSwitch = 1
CustomMenuType = 2
EnableVipShopButton = 1
EnableRankingButton = 1
EnableCommandButton = 1
EnableOptionButton = 1
EnableEventTimeButton = 1

;==================================================
; Enable Windows Custom
; 0: Disable
; 1: Enable
;==================================================
EnableWindowsVipShop = 1
EnableWindowsRankTop = 1
EnableWindowsCommand = 1
EnableWindowsEventTime = 1

;==================================================
; - SwitchPersonalShopType
; 0: Normal
; 1: Premium
; - Enable Custom Store Buttons (only PersonalShopType = 0)
; 0: Disable
; 1: Enable
;==================================================
SwitchPersonalShopType = 1
EnableOffStoreButtom = 1
EnableStoreBlessButtom = 1
EnableStoreSoulButtom = 1
EnableStoreChaosButtom = 1
EnableStoreCoin1Buttom = 1
EnableStoreCoin2Buttom = 1
EnableStoreCoin3Buttom = 1

;==================================================
; Scene Render InGame
; 0: Normal ->          [Login: World74][Character: World75]
; 1: Downgrade S1
; 2: Downgrade S2/S3 -> [Login: World56][Character: World55]
; 3: Downgrade S4 ->    [Login: World78][Character: World79]
; 4: Upgrade (S13)  ->  [Login: World95][Character: World94]
;==================================================
loginScene = 4
characterScene = 4

[Interface]
;==================================================
; get server name in ServerList.bmd or in ServerList ConnectServer
; 0: ConnectServer
; 1: ServerList.bmd
;==================================================
DefaultServerName = 1

;==================================================
; System screen size and resize
; 0: Normal
; 1: Widescreen height
; 2: Widescreen size extension all
;==================================================
screenMode = 1

;==================================================
; Look and feel Integration
; 0: Normal
; 1: Downgrade s1    -> (Req: HUD/Look-1/)
; 2: Downgrade s2/s3 -> (Req: HUD/Look-2/)
; 3: Ex700 (s16)     -> (Req: HUD/Look-3/)
; 4: Legends         -> (Req: HUD/Look-4/)
;==================================================
uiTheme = 0

;==================================================
; Char Info Balloon (Select Character)
; 0: Normal
; 1: Show Reset -> (Req: character_ex(1).ozt)
; 2: Show Reset + MasterReset -> (Req: character_ex(2).ozt)
;==================================================
nameBalloonStyle = 2

;==================================================
; Custom chat emoticon
; 0: Disable
; 1: Emoticon -> (newui_chat_back.OZJ)
; 2: Blocked Wissper -> (newui_chat_back.OZJ)
; 3: Emoticon + Blocked Wissper -> (newui_chat_back.OZJ)
;==================================================
emoticonSupport = 0

;==================================================
; Inventory Extension
; 1: Enable
; 0: Disable
; MaxInventoryExtension max 4
;==================================================
inventoryExpansion = 1
maxInventoryExpansion = 4

;==================================================
; WareHouse Extension
; 1: Enable
; 0: Disable
;==================================================
warehouseExpansionEnabled = 1

;==================================================
; Interface CashShop InGame
; 1: Enable
; 0: Disable
;==================================================
CashShopInGame = 1

;==================================================
; Interface (MuHelper)
; 1: Enable
; 0: Disable
;==================================================
OficialMuHelper = 1

;==================================================
; Interface MasterSkillTree
; 1: Enable
; 0: Disable
;==================================================
MasterSkill = 1

;==================================================
; Interface view Time Invasion and MonsterKill
; 1: Enable
; 0: Disable
;==================================================
InvasionInfoTime = 1

;==================================================
; Interface S18 view item
; 1: Enable
; 0: Disable
;==================================================
legacyTooltipEnabled = 1
legacyItemDropNames = 1
legacyCharacterNames = 1
RenderEquipmentEarring = 0
RenderEquipmentFlagNat = 1
RenderEquipmentCostume = 1
RenderEquipmentPetMuun = 0
RenderEquipmentPetEagle = 0
