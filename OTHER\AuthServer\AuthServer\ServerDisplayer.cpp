// ServerDisplayer.cpp: implementation of the CServerDisplayer class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "ServerDisplayer.h"
#include "ClientManager.h"
#include "Log.h"
#include "SocketManager.h"

CServerDisplayer gServerDisplayer;
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CServerDisplayer::CServerDisplayer() // OK
{
	for (int n = 0; n < MAX_LOG_TEXT_LINE; n++)
	{
		memset(&this->m_log[n], 0, sizeof(this->m_log[n]));
	}

	this->m_font = CreateFont(50, 0, 0, 0, FW_THIN, 0, 0, 0, ANSI_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, NULL);

	this->m_brush[LOG_INACTIVE_BRUSH] = CreateSolidBrush(RGB(47, 47, 47));
	this->m_brush[LOG_ACTIVE_BRUSH] = CreateSolidBrush(RGB(0, 190, 0));
	this->m_brush[LOG_NAME_BRUSH] = CreateSolidBrush(RGB(33, 33, 33));
	this->m_brush[LOG_MAIN_BRUSH] = CreateSolidBrush(RGB(33, 33, 33)); //-- log general

	strcpy_s(this->m_DisplayerText[0], "STANDBY MODE");
	strcpy_s(this->m_DisplayerText[1], "ACTIVE MODE");

	m_hBitmap = (HBITMAP)LoadImage(NULL, ".\\Data\\win32.api", IMAGE_BITMAP, 0, 0, LR_LOADFROMFILE);
}

CServerDisplayer::~CServerDisplayer() // OK
{
	DeleteObject(this->m_font);
	DeleteObject(this->m_brush[LOG_INACTIVE_BRUSH]);
	DeleteObject(this->m_brush[LOG_ACTIVE_BRUSH]);
	DeleteObject(this->m_brush[LOG_NAME_BRUSH]);
	DeleteObject(this->m_brush[LOG_MAIN_BRUSH]);

	if (m_hBitmap != nullptr)
	{
		DeleteObject(m_hBitmap);
		m_hBitmap = nullptr;
	}
}

void CServerDisplayer::Init(HWND hWnd) // OK
{
	this->m_hwnd = hWnd;

	gLog.AddLog(1, "LOG");
}

void CServerDisplayer::Run() // OK
{
	this->SetWindowName();
	this->PaintAllInfo();
	this->LogTextPaint();
	this->PaintName();
}

void CServerDisplayer::SetWindowName() // OK
{
	char buff[256];

	wsprintf(buff, "[%s] AuthServer (QueueSize : %d)", AUTHSERVER_VERSION, gSocketManager.GetQueueSize());

	SetWindowText(this->m_hwnd, buff);
}

void CServerDisplayer::PaintAllInfo() // OK
{
	RECT rect;

	GetClientRect(this->m_hwnd, &rect);

	int PosY = 150;
	rect.top = PosY;
	rect.bottom = PosY + 50;

	HDC hdc = GetDC(this->m_hwnd);

	int OldBkMode = SetBkMode(hdc, TRANSPARENT);

	HFONT OldFont = (HFONT)SelectObject(hdc, this->m_font);

	int state = 0;

	for (int n = 0; n < MAX_CLIENT; n++)
	{
		CClientManager* ClientManager = &gClientManager[n];

		if (ClientManager->CheckState() == 0)
		{
			continue;
		}

		if (ClientManager->GetTimePacket() <= 60000)
		{
			state = 1;
			break;
		}
	}

	if (state == 0)
	{
		SetTextColor(hdc, RGB(200, 200, 200));

		FillRect(hdc, &rect, this->m_brush[0]);

		TextOut(hdc, 120, PosY, this->m_DisplayerText[0], strlen(this->m_DisplayerText[0]));
	}
	else
	{
		SetTextColor(hdc, RGB(250, 250, 250));

		FillRect(hdc, &rect, this->m_brush[1]);

		TextOut(hdc, 150, PosY, this->m_DisplayerText[1], strlen(this->m_DisplayerText[1]));
	}

	SelectObject(hdc, OldFont);

	SetBkMode(hdc, OldBkMode);

	ReleaseDC(this->m_hwnd, hdc);
}

void CServerDisplayer::PaintName() // OK
{
	if (m_hBitmap != nullptr)
	{
		RECT Rect;
		HDC hdc = GetDC(this->m_hwnd);
		GetClientRect(this->m_hwnd, &Rect);

		HDC  hdcMem = CreateCompatibleDC(hdc);
		HBITMAP hBitmapOld = (HBITMAP)SelectObject(hdcMem, m_hBitmap); // Usar m_hBitmap

		Rect.top = 0;
		Rect.bottom = 150;
		FillRect(hdc, &Rect, this->m_brush[LOG_NAME_BRUSH]);
		StretchBlt(hdc, 0, 0, 600, 150, hdcMem, 0, 0, 600, 150, SRCCOPY);


		SelectObject(hdcMem, hBitmapOld);
		DeleteDC(hdcMem);
		ReleaseDC(this->m_hwnd, hdc);
	}
}

void CServerDisplayer::LogTextPaint() // OK
{
	RECT rect;

	GetClientRect(this->m_hwnd, &rect);

	rect.top = 50;

	HDC hdc = GetDC(this->m_hwnd);

	FillRect(hdc, &rect, (HBRUSH)GetStockObject(0));

	int line = MAX_LOG_TEXT_LINE;

	int count = (((this->m_count - 1) >= 0) ? (this->m_count - 1) : (MAX_LOG_TEXT_LINE - 1));

	for (int n = 0; n < MAX_LOG_TEXT_LINE; n++)
	{
		switch (this->m_log[count].color)
		{
		case LOG_BLACK:
			SetTextColor(hdc, CLRREF_WHITE);
			break;
		case LOG_RED:
			SetTextColor(hdc, CLRREF_RED);
			break;
		case LOG_GREEN:
			SetTextColor(hdc, CLRREF_GREEN);
			break;
		case LOG_BLUE:
			SetTextColor(hdc, CLRREF_BLUE);
			break;
		}

		int size = strlen(this->m_log[count].text);

		if (size > 1)
		{
			TextOut(hdc, 0, (85 + (line * 15)), this->m_log[count].text, size);
			line--;
		}

		count = (((--count) >= 0) ? count : (MAX_LOG_TEXT_LINE - 1));
	}

	ReleaseDC(this->m_hwnd, hdc);
}

void CServerDisplayer::LogAddText(eLogColor color, char* text, int size) // OK
{
	size = ((size >= MAX_LOG_TEXT_SIZE) ? (MAX_LOG_TEXT_SIZE - 1) : size);

	memset(&this->m_log[this->m_count].text, 0, sizeof(this->m_log[this->m_count].text));

	memcpy(&this->m_log[this->m_count].text, text, size);

	this->m_log[this->m_count].color = color;

	this->m_count = (((++this->m_count) >= MAX_LOG_TEXT_LINE) ? 0 : this->m_count);

	gLog.Output(LOG_GENERAL, "%s", &text[9]);
}
