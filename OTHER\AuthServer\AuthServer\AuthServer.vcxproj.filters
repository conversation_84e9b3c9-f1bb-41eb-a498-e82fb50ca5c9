﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <None Include="ReadMe.txt" />
    <None Include="small.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="AuthServer.ico">
      <Filter>Resource Files</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="stdafx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AuthServer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CriticalSection.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="QueryManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Queue.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ServerDisplayer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SocketManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AuthServerProtocol.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="IpManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ThemidaSDK.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Log.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ClientManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MiniDump.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="stdafx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AuthServer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CriticalSection.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="QueryManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ServerDisplayer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SocketManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Queue.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AuthServerProtocol.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Util.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="IpManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Log.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ClientManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MiniDump.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="AuthServer.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
</Project>