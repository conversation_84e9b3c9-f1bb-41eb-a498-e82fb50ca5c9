#version 330

layout(location = 0) in vec3 aPos;
layout(location = 1) in vec3 aNorm;
layout(location = 2) in vec2 aTex;
layout(location = 3) in uint aBone;

uniform mat4 uProj;
uniform mat4 uView;
uniform vec4 u_Bones[200];

uniform vec4 u_bodyLight;
uniform vec4 u_lightPosition;
uniform vec4 u_meshUV;
uniform vec4 u_setting1;
uniform vec4 u_setting2;
uniform bool u_enableLight;

out vec4 v_color0;
out vec2 v_texcoord0;

void main() {
    int offset = int(aBone);
	vec4 BoneTransform;
    BoneTransform.x = dot(u_Bones[offset], vec4(aPos, 1.0));
    BoneTransform.y = dot(u_Bones[offset + 1], vec4(aPos, 1.0));
    BoneTransform.z = dot(u_Bones[offset + 2], vec4(aPos, 1.0));
    BoneTransform.w = 1.0;
	
    vec4 tmpvar_2;
    tmpvar_2 = BoneTransform;
	
	gl_Position = uProj * uView * tmpvar_2;
	
	vec3 NorTransform;
    NorTransform.x = dot(u_Bones[offset].xyz, aNorm);
    NorTransform.y = dot(u_Bones[offset + 1].xyz, aNorm);
    NorTransform.z = dot(u_Bones[offset + 2].xyz, aNorm);
	
	vec3 tmpvar_3;
	tmpvar_3 = normalize(NorTransform);
	
	if(u_enableLight)
	{
	   	float tmpvar_4;
		tmpvar_4 = max(((((dot(tmpvar_3, u_lightPosition.xyz) * 0.8) + 0.4)* u_lightPosition.w) + (1.0 - u_lightPosition.w)), 0.2);
        vec4 tmpvar_5;
        tmpvar_5.w = u_lightPosition.w;
        tmpvar_5.x = tmpvar_4;
        tmpvar_5.y = tmpvar_4;
        tmpvar_5.z = tmpvar_4;
		v_color0 = clamp((u_bodyLight * tmpvar_5), 0.0, 1.0);
	}
	else
	{
	  	v_color0 = u_bodyLight;	
	}
	vec2 tmpvar_6;
	tmpvar_6.x = dot(tmpvar_3, u_setting1.xyz);
    tmpvar_6.y = 1.f - dot(tmpvar_3, u_setting1.xyz);
	vec2 tmpvar_7;
	tmpvar_7.y = tmpvar_6.y - (tmpvar_3.z * u_setting2.x) + ( u_setting1.w * u_setting2.y);
	tmpvar_7.x = tmpvar_6.x + (tmpvar_3.y * u_setting2.z) + ( u_setting1.y * u_setting2.w);
	v_texcoord0 = (tmpvar_7 + u_meshUV.xy);
}