# MU2-BECKER项目架构图表分析

## 1. 系统整体架构图

### 1.1 服务器集群架构

```mermaid
graph TB
    subgraph "客户端层"
        C1[游戏客户端1]
        C2[游戏客户端2]
        C3[游戏客户端N]
    end
    
    subgraph "网络接入层"
        CS[ConnectServer<br/>连接服务器<br/>Port: 44405]
    end
    
    subgraph "业务逻辑层"
        JS[JoinServer<br/>登录服务器<br/>Port: 55970]
        GS1[GameServer1<br/>游戏服务器<br/>Port: 55901]
        GS2[GameServer2<br/>游戏服务器<br/>Port: 55902]
        GSN[GameServerN<br/>游戏服务器<br/>Port: 5590N]
    end
    
    subgraph "数据服务层"
        DS[DataServer<br/>数据服务器<br/>Port: 55960]
        DB[(SQL Server<br/>数据库)]
    end
    
    subgraph "认证服务层"
        AS[AuthServer<br/>认证服务器]
    end
    
    C1 --> CS
    C2 --> CS
    C3 --> CS
    
    CS --> JS
    JS --> GS1
    JS --> GS2
    JS --> GSN
    
    GS1 --> DS
    GS2 --> DS
    GSN --> DS
    
    DS --> DB
    
    CS --> AS
    JS --> AS
    GS1 --> AS
```

### 1.2 客户端架构图

```mermaid
graph TB
    subgraph "应用层"
        MAIN[Main5.2<br/>主程序]
        UI[UI系统]
        GAME[游戏逻辑]
    end
    
    subgraph "渲染层"
        RENDER[渲染引擎]
        OGL[OpenGL]
        RMLUI[RmlUI框架]
        VLC[VLC媒体]
    end
    
    subgraph "网络层"
        NET[网络通信]
        PROTO[协议处理]
        ENCRYPT[加密模块]
    end
    
    subgraph "资源层"
        RES[资源管理]
        TEX[纹理管理]
        MODEL[模型管理]
        SOUND[音频管理]
    end
    
    subgraph "系统层"
        INPUT[输入系统]
        FILE[文件系统]
        CONFIG[配置系统]
    end
    
    MAIN --> UI
    MAIN --> GAME
    UI --> RENDER
    GAME --> RENDER
    
    RENDER --> OGL
    RENDER --> RMLUI
    RENDER --> VLC
    
    GAME --> NET
    NET --> PROTO
    NET --> ENCRYPT
    
    RENDER --> RES
    RES --> TEX
    RES --> MODEL
    RES --> SOUND
    
    MAIN --> INPUT
    MAIN --> FILE
    MAIN --> CONFIG
```

## 2. 数据流图

### 2.1 用户登录数据流

```mermaid
sequenceDiagram
    participant C as 客户端
    participant CS as ConnectServer
    participant JS as JoinServer
    participant DS as DataServer
    participant DB as 数据库
    participant GS as GameServer
    
    C->>CS: 1. 连接请求
    CS->>C: 2. 服务器列表
    C->>CS: 3. 选择服务器
    CS->>JS: 4. 转发连接
    
    C->>JS: 5. 账号密码
    JS->>DS: 6. 验证请求
    DS->>DB: 7. 查询账号
    DB->>DS: 8. 返回结果
    DS->>JS: 9. 验证结果
    
    JS->>GS: 10. 分配服务器
    JS->>C: 11. 登录成功
    C->>GS: 12. 连接游戏服务器
    
    GS->>DS: 13. 加载角色数据
    DS->>DB: 14. 查询角色
    DB->>DS: 15. 返回角色数据
    DS->>GS: 16. 角色数据
    GS->>C: 17. 进入游戏
```

### 2.2 游戏操作数据流

```mermaid
sequenceDiagram
    participant C as 客户端
    participant GS as GameServer
    participant DS as DataServer
    participant DB as 数据库
    participant OC as 其他客户端
    
    C->>GS: 1. 游戏操作(移动/攻击/技能)
    GS->>GS: 2. 验证操作合法性
    GS->>GS: 3. 更新游戏状态
    
    alt 需要持久化
        GS->>DS: 4. 数据更新请求
        DS->>DB: 5. 更新数据库
        DB->>DS: 6. 确认更新
        DS->>GS: 7. 更新确认
    end
    
    GS->>C: 8. 操作结果
    GS->>OC: 9. 广播状态变化
```

## 3. 类关系图

### 3.1 服务器端核心类关系

```mermaid
classDiagram
    class CObjectManager {
        +gObjAddSearch()
        +gObjAdd()
        +gObjDel()
        +gObjUserAct()
        +gObjMonsterAct()
    }
    
    class CUser {
        +Index: int
        +Account: char[]
        +Name: char[]
        +Level: int
        +Class: int
        +Map: int
        +X, Y: int
    }
    
    class CMonster {
        +Class: int
        +Level: int
        +Life: int
        +MaxLife: int
        +AI: CMonsterAI
    }
    
    class CSkillManager {
        +AddSkill()
        +UseSkill()
        +CheckSkillRequire()
        +RunningSkill()
    }
    
    class CItemManager {
        +CreateItem()
        +DropItem()
        +CheckItemRequire()
        +ItemValue()
    }
    
    class CMapManager {
        +LoadMap()
        +CheckMove()
        +GetMapAttr()
        +SetMapAttr()
    }
    
    CObjectManager --> CUser
    CObjectManager --> CMonster
    CUser --> CSkillManager
    CUser --> CItemManager
    CObjectManager --> CMapManager
```

### 3.2 客户端UI类关系

```mermaid
classDiagram
    class CUIMng {
        +m_MsgWin: CMsgWin
        +m_LoginMainWin: CLoginMainWin
        +m_CharSelMainWin: CCharSelMainWin
        +SetScene()
        +ProcessInput()
        +Render()
    }
    
    class CNewUIManager {
        +AddUIObj()
        +RemoveUIObj()
        +UpdateUIObj()
        +RenderUIObj()
    }
    
    class CNewUIMainFrameWindow {
        +Create()
        +Render()
        +Render3D()
        +RenderFrame()
        +RenderLifeMana()
    }
    
    class CNewUI3DRenderMng {
        +Add3DRenderObj()
        +Remove3DRenderObj()
        +RenderUI2DEffect()
    }
    
    class RenderInterface_GL3 {
        +RenderGeometry()
        +CompileGeometry()
        +SetViewport()
    }
    
    CUIMng --> CNewUIManager
    CNewUIManager --> CNewUIMainFrameWindow
    CNewUIMainFrameWindow --> CNewUI3DRenderMng
    CNewUI3DRenderMng --> RenderInterface_GL3
```

## 4. 状态机图

### 4.1 客户端状态机

```mermaid
stateDiagram-v2
    [*] --> 初始化
    初始化 --> 标题画面
    标题画面 --> 登录界面
    登录界面 --> 服务器选择
    服务器选择 --> 角色选择
    角色选择 --> 角色创建
    角色创建 --> 角色选择
    角色选择 --> 加载中
    加载中 --> 游戏中
    
    游戏中 --> 游戏中 : 正常游戏操作
    游戏中 --> 断线重连 : 网络断开
    断线重连 --> 游戏中 : 重连成功
    断线重连 --> 登录界面 : 重连失败
    
    游戏中 --> 角色选择 : 返回角色选择
    角色选择 --> 登录界面 : 注销
    登录界面 --> [*] : 退出游戏
```

### 4.2 服务器对象状态机

```mermaid
stateDiagram-v2
    [*] --> OFFLINE
    OFFLINE --> CONNECTED : 客户端连接
    CONNECTED --> LOGGED : 登录验证成功
    LOGGED --> PLAYING : 进入游戏世界
    
    PLAYING --> PLAYING : 正常游戏操作
    PLAYING --> LOGGED : 返回角色选择
    LOGGED --> CONNECTED : 注销
    CONNECTED --> OFFLINE : 断开连接
    
    PLAYING --> OFFLINE : 异常断开
    LOGGED --> OFFLINE : 异常断开
    CONNECTED --> OFFLINE : 异常断开
    
    OFFLINE --> [*] : 对象销毁
```

## 5. 部署架构图

### 5.1 单机部署架构

```mermaid
graph TB
    subgraph "物理服务器"
        subgraph "进程1"
            CS[ConnectServer]
        end
        
        subgraph "进程2"
            JS[JoinServer]
        end
        
        subgraph "进程3"
            GS[GameServer]
        end
        
        subgraph "进程4"
            DS[DataServer]
        end
        
        subgraph "数据库"
            DB[(SQL Server)]
        end
    end
    
    CS -.-> JS
    JS -.-> GS
    GS -.-> DS
    DS -.-> DB
```

### 5.2 分布式部署架构

```mermaid
graph TB
    subgraph "负载均衡器"
        LB[Load Balancer]
    end
    
    subgraph "连接服务器集群"
        CS1[ConnectServer1]
        CS2[ConnectServer2]
    end
    
    subgraph "登录服务器集群"
        JS1[JoinServer1]
        JS2[JoinServer2]
    end
    
    subgraph "游戏服务器集群"
        GS1[GameServer1<br/>频道1]
        GS2[GameServer2<br/>频道2]
        GS3[GameServer3<br/>频道3]
    end
    
    subgraph "数据服务器集群"
        DS1[DataServer1<br/>主]
        DS2[DataServer2<br/>从]
    end
    
    subgraph "数据库集群"
        DB1[(主数据库)]
        DB2[(从数据库)]
    end
    
    LB --> CS1
    LB --> CS2
    
    CS1 --> JS1
    CS2 --> JS2
    
    JS1 --> GS1
    JS1 --> GS2
    JS2 --> GS3
    
    GS1 --> DS1
    GS2 --> DS1
    GS3 --> DS2
    
    DS1 --> DB1
    DS2 --> DB2
    DB1 -.-> DB2
```

## 6. 网络协议栈

### 6.1 协议层次结构

```mermaid
graph TB
    subgraph "应用层"
        GAME[游戏协议]
        LOGIN[登录协议]
        CHAT[聊天协议]
    end
    
    subgraph "会话层"
        ENCRYPT[加密层]
        COMPRESS[压缩层]
    end
    
    subgraph "传输层"
        TCP[TCP协议]
        UDP[UDP协议]
    end
    
    subgraph "网络层"
        IP[IP协议]
    end
    
    subgraph "数据链路层"
        ETH[以太网]
    end
    
    GAME --> ENCRYPT
    LOGIN --> ENCRYPT
    CHAT --> ENCRYPT
    
    ENCRYPT --> COMPRESS
    COMPRESS --> TCP
    COMPRESS --> UDP
    
    TCP --> IP
    UDP --> IP
    IP --> ETH
```

### 6.2 消息处理流程

```mermaid
graph LR
    subgraph "发送端"
        A1[游戏逻辑] --> A2[消息封装]
        A2 --> A3[加密处理]
        A3 --> A4[网络发送]
    end
    
    subgraph "网络传输"
        A4 --> B1[网络传输]
    end
    
    subgraph "接收端"
        B1 --> B2[网络接收]
        B2 --> B3[解密处理]
        B3 --> B4[消息解析]
        B4 --> B5[业务处理]
    end
```

---

*本架构图表分析文档配合主分析报告使用，通过可视化图表展示MU2-BECKER项目的技术架构、数据流、类关系和部署方案。*
