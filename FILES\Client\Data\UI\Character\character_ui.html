<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Select Class - Whisperer</title>
  <style>
    body {
      margin: 0;
      background-color: #111;
      font-family: Arial, sans-serif;
      color: white;
      background-image: url("bg.jpg"); /* Opcional */
      background-size: cover;
    }

    .container {
      display: flex;
      justify-content: space-between;
      padding: 40px;
    }

    .character {
      flex: 1;
      text-align: center;
    }

    .character img {
      height: 600px;
    }

    .class-details {
      flex: 1;
      padding-left: 40px;
      position: relative;
    }

    .class-name {
      font-size: 36px;
      color: #00bfff;
    }

    .description {
      margin: 20px 0;
      max-width: 500px;
    }

    .stats {
      margin: 20px 0;
    }

    .stat {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 8px 0;
  width: 300px;
}

.stat-label {
  width: 70px;
  text-align: left;
  font-weight: bold;
}

.bar {
  flex: 1;
  height: 10px;
  background: #444;
  border-radius: 5px;
  margin-left: 10px;
  overflow: hidden;
}

    .bar-fill {
      height: 100%;
      background: #f90;
      display: inline-block;
    }

    .buttons {
      margin-top: 30px;
    }

    .buttons button {
      background: #222;
      color: #fff;
      padding: 10px 20px;
      border: 1px solid #666;
      margin-right: 10px;
      cursor: pointer;
    }

    .preview-img {
      width: 300px;
      margin-top: 20px;
      border: 1px solid #555;
    }

    .class-icons {
      position: absolute;
      top: 0;
      right: -150px;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .class-icon {
      width: 100px;
      height: 100px;
      background-size: cover;
      border: 2px solid #666;
      border-radius: 10px;
      opacity: 0.5;
      cursor: pointer;
      transition: transform 0.2s, opacity 0.2s;
    }

    .class-icon:hover {
      opacity: 0.8;
      transform: scale(1.05);
    }

    .class-icon.active {
      border-color: #00bfff;
      opacity: 1;
    }
  </style>
</head>
<body>

  <div class="container">
    <!-- Personaje principal -->
    <div class="character">
      <img src="whisperer.png" alt="Whisperer">
    </div>

    <!-- Detalles de clase y selección -->
    <div class="class-details">
      <div class="class-name">Whisperer</div>
      <div class="description">
        Powerful ranged damage dealer with high mobility. Capable of employing a wide range of strategies, utilizing not only bows but also traps, smoke shells, and the power of nature.
      </div>

      <div class="stat">
  <div class="stat-label">Attack:</div>
  <div class="bar"><div class="bar-fill" style="width: 90%"></div></div>
</div>
<div class="stat">
  <div class="stat-label">HP:</div>
  <div class="bar"><div class="bar-fill" style="width: 50%"></div></div>
</div>
<div class="stat">
  <div class="stat-label">Co-op:</div>
  <div class="bar"><div class="bar-fill" style="width: 70%"></div></div>
</div>

      <img src="combat_preview.png" class="preview-img" alt="Combat Preview">

      <div class="buttons">
        <button>See Recommended Tactics</button>
        <button>Appearance Settings ▶</button>
      </div>

      <!-- Iconos de clase -->
      <div class="class-icons">
        <div class="class-icon" style="background-image: url('blader_icon.jpg');"></div>
        <div class="class-icon" style="background-image: url('darklord_icon.jpg');"></div>
        <div class="class-icon" style="background-image: url('warmage_icon.jpg');"></div>
        <div class="class-icon active" style="background-image: url('whisperer_icon.jpg');"></div>
        <div class="class-icon" style="background-image: url('spellbinder_icon.jpg');"></div>
      </div>
    </div>
  </div>

</body>
</html>
