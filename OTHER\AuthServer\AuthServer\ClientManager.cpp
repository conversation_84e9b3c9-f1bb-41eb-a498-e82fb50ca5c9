// ClientManager.cpp: implementation of the CClientManager class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "ClientManager.h"
#include "IpManager.h"
#include "Util.h"

CClientManager gClientManager[MAX_CLIENT];
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CClientManager::CClientManager() // OK
{
	this->Initialize();
}

CClientManager::~CClientManager() // OK
{
}

void CClientManager::Initialize()
{
	this->m_index = -1;

	this->m_state = eClientState::CLIENT_OFFLINE;

	this->m_socket = INVALID_SOCKET;

	this->m_IoRecvContext = 0;

	this->m_IoSendContext = 0;
}

bool CClientManager::CheckState() // OK
{
	if (CLIENT_RANGE(this->m_index) == 0 || this->m_state == eClientState::CLIENT_OFFLINE || this->m_socket == INVALID_SOCKET)
	{
		return 0;
	}

	return 1;
}

bool CClientManager::CheckOnlineTime() // OK
{
	return !(this->GetTimeOnline() > MAX_ONLINE_TIME);
}

void CClientManager::AddClient(int index, char* ip, SOCKET socket) // OK
{
	this->m_index = index;

	this->m_state = eClientState::CLIENT_ONLINE;

	strcpy_s(this->m_IpAddr, ip);

	this->m_socket = socket;

	this->m_IoRecvContext = ((this->m_IoRecvContext == 0) ? (new IO_RECV_CONTEXT) : this->m_IoRecvContext);

	this->m_IoSendContext = ((this->m_IoSendContext == 0) ? (new IO_SEND_CONTEXT) : this->m_IoSendContext);

	memset(&this->m_IoRecvContext->overlapped, 0, sizeof(this->m_IoRecvContext->overlapped));

	this->m_IoRecvContext->wsabuf.buf = (char*)this->m_IoRecvContext->IoMainBuffer.buff;
	this->m_IoRecvContext->wsabuf.len = MAX_MAIN_PACKET_SIZE;
	this->m_IoRecvContext->IoType = IO_RECV;
	this->m_IoRecvContext->IoSize = 0;
	this->m_IoRecvContext->IoMainBuffer.size = 0;

	memset(&this->m_IoSendContext->overlapped, 0, sizeof(this->m_IoSendContext->overlapped));

	this->m_IoSendContext->wsabuf.buf = (char*)this->m_IoSendContext->IoMainBuffer.buff;
	this->m_IoSendContext->wsabuf.len = MAX_MAIN_PACKET_SIZE;
	this->m_IoSendContext->IoType = IO_SEND;
	this->m_IoSendContext->IoSize = 0;
	this->m_IoSendContext->IoMainBuffer.size = 0;
	this->m_IoSendContext->IoSideBuffer.size = 0;

	this->time_online = std::chrono::steady_clock::now();

	this->time_packet = std::chrono::steady_clock::time_point();

	gIpManager.InsertIpAddress(this->m_IpAddr);
}

void CClientManager::DelClient() // OK
{
	gIpManager.RemoveIpAddress(this->m_IpAddr);

	this->m_index = -1;

	this->m_state = eClientState::CLIENT_OFFLINE;

	memset(this->m_IpAddr, 0, sizeof(this->m_IpAddr));

	this->m_socket = INVALID_SOCKET;

	this->time_online = std::chrono::steady_clock::time_point();

	this->time_packet = std::chrono::steady_clock::time_point();
}

long long CClientManager::GetTimePacket()
{
	auto now = std::chrono::steady_clock::now();
	auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - this->time_packet);

	return duration.count();
}

long long CClientManager::GetTimeOnline()
{
	auto now = std::chrono::steady_clock::now();
	auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - this->time_online);

	return duration.count();
}
