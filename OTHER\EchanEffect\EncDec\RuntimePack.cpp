#include "StdAfx.h"
#include "SMDToken.h"
#include "RuntimePack.h"

//===========================================================================
//-- FILES SERVERLIST MANAGER
//===========================================================================

void analyze_file_unique(int toked, std::string filename, std::string fileoutput)
{
	if (fopen_s(&SMDFile, filename.c_str(), "rb") != 0)
	{
		return;
	}

	SMDToken Token;

	std::vector<EnchantEffect> enchand_effects;

	while (true)
	{
		Token = (*GetToken)();
		if (Token == END)
			break;
		if (Token == NAME && strcmp("end", TokenString) == NULL)
			break;

		EnchantEffect info;
		int nType = GetTokenNumber();
		int nIndex = GetAsTokenNumber();

		info.itemindex = nIndex + (nType << 9);

		//std::cerr << "itemindex: " << info.itemindex << " Index:" << nType << " Type:" << nIndex << std::endl;
		info.Color[0] = GetAsTokenFloat();
		info.Color[1] = GetAsTokenFloat();
		info.Color[2] = GetAsTokenFloat();
		info.GroupId = GetAsTokenNumber();

		info.RenderFlag = GetAsTokenNumber();
		info.RenderType = GetAsTokenNumber();
		info.TimeEffectType = GetAsTokenFloat();
		info.TextureID = GetAsTokenNumber();
		info.TimeTextureID = GetAsTokenFloat();
		info.NoGlow = GetAsTokenNumber();

		enchand_effects.push_back(info);
	}
	fclose(SMDFile);


	if (!enchand_effects.empty())
	{
		PackFileEncrypt(fileoutput.c_str(), enchand_effects, 0, sizeof(EnchantEffect), 0xE2F1);
	}
}
