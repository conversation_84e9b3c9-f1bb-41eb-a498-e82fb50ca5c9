@spritesheet theme
{
	src: hud_global.png;

	checkbox: 0px 100px 32px 32px;
	checkbox-hover: 0px 100px 32px 32px;
	checkbox-active: 0px 100px 32px 32px;
	checkbox-checked: 32px 100px 32px 32px;
	checkbox-checked-hover: 32px 100px 32px 32px;
	checkbox-checked-active: 32px 100px 32px 32px;

	radio: 0px 132px 32px 32px;
	radio-hover: 0px 132px 32px 32px;
	radio-active: 0px 132px 32px 32px;
	radio-checked: 32px 132px 32px 32px;
	radio-checked-hover: 32px 132px 32px 32px;
	radio-checked-active: 32px 132px 32px 32px;

	selectbox-tl: 4px 0px 11px 10px;
	selectbox-t:  13px 0px 1px 10px;
	selectbox-tr: 122px 0px 11px 10px;

	selectbox-l:  4px 19px 11px 4px;
	selectbox-c:  13px 19px 4px 4px;
	selectbox-r:  128px 19px 11px 4px;

	selectbox-bl: 4px 22px 11px 10px;
	selectbox-b:  13px 22px 1px 10px;
	selectbox-br: 122px 22px 11px 10px;
}

body {
    font-family: Noto Sans SC;
    font-weight: normal;
    font-style: normal;
    font-size: 15dp;
    color: white;
    margin: 0px;
    padding: 0px;
    width: 100%;
    height: 100%;
}

input.checkbox
{
	decorator: image(checkbox);
}

input.checkbox:hover, input.checkbox:focus-visible
{
	decorator: image(checkbox-hover);
}

input.checkbox:active
{
	decorator: image(checkbox-active);
}

input.checkbox:checked
{
	decorator: image(checkbox-checked);
}

input.checkbox:checked:hover, input.checkbox:checked:focus-visible
{
	decorator: image(checkbox-checked-hover);
}

input.checkbox:checked:active
{
	decorator: image(checkbox-checked-active);
}

input.radio
{
	decorator: image(radio);
}

input.radio:hover, input.radio:focus-visible
{
	decorator: image(radio-hover);
}

input.radio:active
{
	decorator: image(radio-active);
}

input.radio:checked
{
	decorator: image(radio-checked);
}

input.radio:checked:hover, input.radio:checked:focus-visible
{
	decorator: image(radio-checked-hover);
}

input.radio:checked:active
{
	decorator: image(radio-checked-active);
}

select selectbox
{
	margin-left: 10px;
	margin-top: 10px;
	margin-bottom: 10px;
	width: 162px;
	padding: 4px 4px 4px 4px;
}

select selectbox,
tbody
{
	decorator: tiled-box(
		selectbox-tl, selectbox-t, selectbox-tr,
		selectbox-l, selectbox-c, selectbox-r,  /* auto mirrors left */
		selectbox-bl, selectbox-b, selectbox-br
	);
}

.login-container {
    position: absolute;
    top: 50%;
    left: 5%;
    transform: translateY(-50%);
    background-color: rgba(10, 10, 10, 178);
    padding: 30px;
    width: 300px;
    border-radius: 10px;
}

.header-img{ 
    display: block;
    margin: 0px;
    padding: 0px;
    width: 300px;
    height: 118px;
    decorator: image(DL_encabezado.png);
    }

.login-container h2 {
    margin-top: 10;
    font-size: 20px;
    font-weight: normal;
    text-align: center;
}

#server_name {
    font-size: 16px;
    display: block;
}

.login-container input {
      width: 94%;
      padding: 10px;
      margin: 10px 0;
      border: 1 px;
      border-radius: 5px;
      background-color: #1e1e1e;
      color: #fff;
    }

input::placeholder {
    color: #ccc;
    font-style: italic;
}

.login-container button {
    display: block; /* fuerza salto de línea */
    width: 94%;
    padding: 10px;
    margin-top: 10px;
    border: none;
    border-radius: 5px;
    background: #b59142;
    color: #fff;
    font-weight: bold;
    text-align: center;        /* ⬅ CENTRA el texto horizontalmente */
    line-height: normal;       /* Asegura alineación vertical estándar */
}

.button-row {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 10px;
}

.button-row button {
    display: inline-block;
    width: 50%; /* o el tamaño que prefieras */
}

#btn_register {
    margin-top: 10px;
    margin-right: 10px;
}

.login-container input[type="checkbox"]
{
    width: 12px;
    height: 12px;
    margin-right: 8px;
    vertical-align: middle;
}

.login-container select {

    display: block;
    width: 200px;
    height: 30px;
    font-size: 20px;
    margin: 10px;
}