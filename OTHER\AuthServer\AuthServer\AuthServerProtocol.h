#pragma once

enum eAuthServerType
{
	AUTH_SERVER_TYPE_NONE = 0,
	AUTH_SERVER_TYPE_S6_CONNECT_SERVER = 1,
	AUTH_SERVER_TYPE_S6_JOIN_SERVER = 2,
	AUTH_SERVER_TYPE_S6_DATA_SERVER = 3,
	AUTH_SERVER_TYPE_S6_GAME_SERVER = 4,
	AUTH_SERVER_TYPE_S8_CONNECT_SERVER = 5,
	AUTH_SERVER_TYPE_S8_JOIN_SERVER = 6,
	AUTH_SERVER_TYPE_S8_DATA_SERVER = 7,
	AUTH_SERVER_TYPE_S8_GAME_SERVER = 8,
	AUTH_SERVER_TYPE_S4_CONNECT_SERVER = 9,
	AUTH_SERVER_TYPE_S4_JOIN_SERVER = 10,
	AUTH_SERVER_TYPE_S4_DATA_SERVER = 11,
	AUTH_SERVER_TYPE_S4_GAME_SERVER = 12,
	AUTH_SERVER_TYPE_S2_CONNECT_SERVER = 13,
	AUTH_SERVER_TYPE_S2_JOIN_SERVER = 14,
	AUTH_SERVER_TYPE_S2_DATA_SERVER = 15,
	AUTH_SERVER_TYPE_S2_GAME_SERVER = 16,
};

enum eAuthServerStatus
{
	AUTH_SERVER_STATUS_NONE = 0,
	AUTH_SERVER_STATUS_SUCCESS = 1,
};

struct SDHP_AUTH_SERVER_DATA_RECV
{
	BYTE type; // C1:00
	BYTE size;
	BYTE head;
	BYTE EncKey;
	int ServerType;
	char CustomerName[32];
	char CustomerHardwareId[36];
};

struct SDHP_AUTH_SERVER_ONLINE_RECV
{
	BYTE type; // C1:00
	BYTE size;
	BYTE head;
	BYTE EncKey;
	char CustomerName[32];
};

struct SDHP_AUTH_SERVER_DATA_SEND
{
	BYTE type; // C1:00
	BYTE size;
	BYTE head;
	BYTE EncKey;
	BYTE ServerType;
	BYTE Status;
	BYTE PackageType;
	BYTE PlanType;
	char CustomerName[32];
	char CustomerHardwareId[36];
};

struct SDHP_AUTH_SERVER_ONLINE_SEND
{
	BYTE type; // C1:00
	BYTE size;
	BYTE head;
	BYTE EncKey;
	BYTE result;
};

typedef struct
{
	int Id;
	char Name[50];
}MEMB_INFO_AUTOR;


typedef struct
{
	WORD Port;
	int Size;
	char AddressIp[16];
}SDHP_CONNECTION_SERVER;

typedef std::vector<MEMB_INFO_AUTOR> type_map_memb;

extern type_map_memb m_MembInfo;


extern void PackageEnc(void* Buffer, int size, BYTE iKey);
extern void PackageDec(void* Buffer, int size, BYTE iKey);
extern void AuthServerProtocolCore(int index, BYTE head, BYTE* lpMsg, int size);
extern void AGAuthServerDataRecv(SDHP_AUTH_SERVER_DATA_RECV* lpMsg, int index);
extern void AGAuthServerOnlineRecv(SDHP_AUTH_SERVER_ONLINE_RECV* lpMsg, int index);


extern void GenerateFileCon(HWND hDlg);
extern void GenerateLicense(HWND hDlg, bool Crear);
extern void AccountCreate(TCHAR* name, TCHAR* email, TCHAR* pass);
