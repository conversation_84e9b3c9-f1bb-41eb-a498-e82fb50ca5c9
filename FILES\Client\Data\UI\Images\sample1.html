<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Login - League Style</title>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      font-family: "Segoe UI", sans-serif;
      height: 100%;
      background: url('seunghee-lee-color8f3c5f-1920.jpg') no-repeat center center;
      background-size: cover;
      color: #fff;
    }

    .overlay {
      background: rgba(0, 0, 0, 0.6);
      position: absolute;
      top: 0; left: 0;
      width: 100%;
      height: 100%;
    }

    .login-container {
      position: absolute;
      top: 50%;
      right: 5%;
      transform: translateY(-50%);
      background: rgba(10, 10, 10, 0.7);
      padding: 30px;
      width: 300px;
      border-radius: 10px;
      box-shadow: 0 0 10px #000;
    }

    .login-container h2 {
      margin-top: 0;
      font-size: 20px;
      font-weight: normal;
    }

    .login-container input {
      width: 100%;
      padding: 10px;
      margin: 10px 0;
      border: none;
      border-radius: 5px;
      background-color: #1e1e1e;
      color: #fff;
    }

    .login-container button {
      width: 100%;
      padding: 10px;
      border: none;
      border-radius: 5px;
      background: #b59142;
      color: #fff;
      cursor: pointer;
      font-weight: bold;
    }

    .login-container .options {
      margin-top: 15px;
      font-size: 12px;
    }

    .bottom-links {
      position: absolute;
      bottom: 10px;
      left: 10px;
      font-size: 12px;
    }

    .bottom-links input[type="checkbox"] {
      margin-right: 5px;
    }

/* Contenedor general */
.progress-container {
      width: 400px;
      height: 25px;
      border-radius: 20px;
      border: 2px solid #74c0fc;
      position: relative;
      overflow: hidden;
      background: rgba(255, 255, 255, 0.05);
    }

    .progress-bar {
      height: 100%;
      background: linear-gradient(to right, #ff6ec4, #7873f5);
      width: 0%;
      border-radius: 20px;
      transition: width 0.3s ease-in-out;
    }

    .progress-text {
      position: absolute;
      width: 100%;
      height: 100%;
      text-align: center;
      line-height: 25px;
      color: #d0d8ff;
      font-weight: bold;
      font-size: 14px;
    }

  </style>
</head>
<body>
  <div class="overlay"></div>
  <div class="login-container">
    <img src="dk encabezado.png" alt="League of Legends" style="width: 100%; height: auto; margin-bottom: 20px;">
    <h2>INICIAR SESSION</h2>
    <input type="text" placeholder="Nome de usuário">
    <input type="password" placeholder="Senha">
    <button>LOGIN</button>
    <button style="background-color: #2f2f2f; margin-top: 10px;">CREAR CUENTA</button>
    <div class="options">
      <label style="display: inline-flex; align-items: center; gap: 5px;"><input type="checkbox"> Recordarme</label>
      <br>
      <a href="#" style="color: #ccc;">Recuperar contraseña</a>
    </div>
  </div>
  <!-- Barra de progreso -->
  <!-- Progress bar definition -->
  <div class="progress-container" data-value="100">
    <div class="progress-bar"></div>
    <div class="progress-text">0%</div>
  </div>
  <div class="bottom-links">
    <label style="display: inline-flex; align-items: center; gap: 5px;"><input type="checkbox"> Desativar as animações de login</label><br>
    <label style="display: inline-flex; align-items: center; gap: 5px;"><input type="checkbox"> Desativar a música de login</label>
  </div>
</body>
</html>
